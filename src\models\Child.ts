import mongoose, { Schema } from 'mongoose';
import bcrypt from 'bcryptjs';
import { IChild } from '../interfaces/child.interface';

const ChildSchema: Schema = new Schema({
  parent: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Parent reference is required']
  },
  fullname: {
    type: String,
    required: [true, 'Full name is required'],
    trim: true,
    maxlength: [100, 'Full name cannot exceed 100 characters']
  },
  username: {
    type: String,
    required: [true, 'Username is required'],
    unique: true,
    trim: true,
    lowercase: true,
    minlength: [3, 'Username must be at least 3 characters'],
    maxlength: [30, 'Username cannot exceed 30 characters'],
    match: [/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores']
  },
  dob: {
    type: Date,
    required: [true, 'Date of birth is required']
  },
  gender: {
    type: String,
    required: [true, 'Gender is required'],
    enum: ['male', 'female', 'other']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters']
  },
  role: {
    type: String,
    enum: ['child'],
    default: 'child'
  }
}, {
  timestamps: true
});

// Hash password before saving
ChildSchema.pre<IChild>('save', async function(next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password as string, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Compare password method
ChildSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

// Remove sensitive data when converting to JSON
ChildSchema.methods.toJSON = function() {
  const childObject = this.toObject();
  delete childObject.password;
  return childObject;
};

export default mongoose.model<IChild>('Child', ChildSchema);
