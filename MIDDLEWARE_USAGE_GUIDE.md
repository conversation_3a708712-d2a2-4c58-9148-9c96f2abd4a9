# Role-Based Middleware Implementation Guide

## Overview
This document explains the comprehensive role-based middleware system implemented for the Piggy Portfolio application. The middleware provides secure authentication and authorization for different user roles.

## Middleware Functions

### 1. `verifyToken`
**Purpose**: Base middleware that extracts and verifies JWT tokens
**Usage**: Used as a foundation for other middleware or standalone for basic authentication
**Sets**: `req.userIdFromToken`, `req.childIdFromToken`, `req.parentIdFromToken`

```typescript
// For routes that need basic authentication (Parent/Admin)
router.get('/profile', verifyToken, getUserProfile);
```

### 2. `verifyTokenChild`
**Purpose**: Verifies child role and ensures child exists in database
**Usage**: For routes that require child authentication
**Requires**: Child login token with `childId` and `parentId`

```typescript
// For child-specific routes
router.get('/child-profile', verifyTokenChild, getChildProfile);
```

### 3. `verifyAdminToken`
**Purpose**: Verifies admin role from User collection
**Usage**: For admin-only routes
**Requires**: User token with Admin role

```typescript
// For admin-only routes
router.get('/admin-dashboard', verifyAdminToken, getAdminDashboard);
```

### 4. `verifyTokenParent`
**Purpose**: Verifies parent role from User collection
**Usage**: For parent-only routes (child management)
**Requires**: User token with Parent role

```typescript
// For parent-only routes
router.post('/add-child', verifyTokenParent, addChild);
```

## Token Types

### User Token (Parent/Admin Login)
```json
{
  "userId": "user_object_id",
  "email": "<EMAIL>"
}
```

### Child Token (Child Login)
```json
{
  "childId": "child_object_id",
  "parentId": "parent_user_object_id"
}
```

## Request Object Extensions

After middleware execution, the following properties are available on `req`:

- `req.userIdFromToken`: User ID from token (Parent/Admin)
- `req.childIdFromToken`: Child ID from child token
- `req.parentIdFromToken`: Parent ID from child token

## Route Configuration Examples

### User Profile Routes (Parent/Admin)
```typescript
router.get('/profile', verifyToken, getUserProfile);
router.put('/profile', verifyToken, updateUserProfile);
router.delete('/profile', verifyToken, deleteUserProfile);
```

### Child Management Routes (Parent Only)
```typescript
router.post('/add-child', verifyTokenParent, addChild);
router.get('/get-all-children', verifyTokenParent, getAllChildren);
router.get('/:childId', verifyTokenParent, getChildDetails);
router.put('/:childId', verifyTokenParent, updateChild);
router.delete('/:childId', verifyTokenParent, deleteChild);
```

### Child-Specific Routes
```typescript
router.get('/child-profile', verifyTokenChild, getChildProfile);
```

### Admin Routes
```typescript
router.get('/admin-users', verifyAdminToken, getAllUsers);
router.delete('/admin-delete-user/:userId', verifyAdminToken, deleteAnyUser);
```

## Error Handling

All middleware functions provide comprehensive error handling:

- **401 Unauthorized**: Missing or invalid token
- **403 Forbidden**: Insufficient role permissions
- **500 Internal Server Error**: Database or server errors

## Controller Updates

Controllers now receive user IDs through middleware-set properties:

```typescript
export const getUserProfile = async (req: Request, res: Response): Promise<void> => {
  const userId = req.userIdFromToken; // Set by middleware
  
  if (!userId) {
    res.status(StatusCodes.UNAUTHORIZED).json({
      success: false,
      message: 'User ID not found in token'
    });
    return;
  }
  
  // Continue with controller logic...
};
```

## Security Features

1. **JWT Verification**: All tokens are verified against JWT_SECRET
2. **Role Validation**: Database lookups ensure role authenticity
3. **Token Type Detection**: Automatic detection of user vs child tokens
4. **Ownership Verification**: Child operations verify parent ownership
5. **Comprehensive Error Handling**: Detailed error messages for debugging

## Migration Notes

- Removed manual role checks from controllers
- Centralized authentication logic in middleware
- Improved error handling and security
- Backward compatibility maintained with legacy function names
