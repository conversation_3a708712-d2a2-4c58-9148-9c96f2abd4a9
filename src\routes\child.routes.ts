import { Router } from 'express';
import {
  loginAsChild,
  getAllChildren,
} from '../controllers/index';
import { verifyTokenForParent } from '../middlewares/auth.middleware';

const router = Router();

// Child login doesn't require authentication (it's the login endpoint)
router.post('/login', loginAsChild);

// Routes that require parent authentication
// GET /api/child/get-all-children - Get all children of parent

// Routes that require child authentication would go here
// router.get('/profile', verifyTokenChild, getChildProfile);

export default router;
