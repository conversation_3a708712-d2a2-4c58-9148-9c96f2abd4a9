// Simple test script for authentication endpoints
// Run this after starting the server: node test-auth.js

const BASE_URL = 'http://localhost:5001/api';

// Test data
const testUser = {
  fullname: 'Test User',
  username: 'testuser',
  email: '<EMAIL>',
  phoneNumber: '+1234567890',
  password: 'password123',
  dob: '1990-01-01',
  gender: 'Male'
};

async function makeRequest(endpoint, method = 'GET', body = null, token = null) {
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  };

  if (token) {
    options.headers.Authorization = `Bearer ${token}`;
  }

  if (body) {
    options.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const data = await response.json();
    return { status: response.status, data };
  } catch (error) {
    console.error('Request failed:', error.message);
    return { status: 500, data: { error: error.message } };
  }
}

async function testAuthFlow() {
  console.log('🚀 Testing Authentication Flow\n');

  // Test 1: Health check
  console.log('1. Testing health check...');
  const health = await makeRequest('/health');
  console.log(`Status: ${health.status}`);
  console.log(`Response:`, health.data);
  console.log('');

  // Test 2: User signup
  console.log('2. Testing user signup...');
  const signup = await makeRequest('/auth/signup', 'POST', testUser);
  console.log(`Status: ${signup.status}`);
  console.log(`Response:`, signup.data);
  console.log('');

  if (signup.status !== 201) {
    console.log('❌ Signup failed, stopping tests');
    return;
  }

  // Test 3: Login without verification (should fail)
  console.log('3. Testing login without verification...');
  const loginUnverified = await makeRequest('/auth/login', 'POST', {
    username: testUser.username,
    password: testUser.password
  });
  console.log(`Status: ${loginUnverified.status}`);
  console.log(`Response:`, loginUnverified.data);
  console.log('');

  // Note: In a real test, you would need to:
  // 1. Check email for OTP
  // 2. Verify OTP with /auth/verify-signup-otp
  // 3. Then test login
  
  console.log('📧 To complete the test:');
  console.log('1. Check the email for OTP');
  console.log('2. Verify OTP using POST /auth/verify-signup-otp');
  console.log('3. Then login using POST /auth/login');
  console.log('');

  // Test 4: Test password reset OTP generation
  console.log('4. Testing password reset OTP generation...');
  const resetOTP = await makeRequest('/auth/forgot-password/generate-otp', 'POST', {
    email: testUser.email
  });
  console.log(`Status: ${resetOTP.status}`);
  console.log(`Response:`, resetOTP.data);
  console.log('');

  console.log('✅ Basic authentication flow tests completed!');
  console.log('');
  console.log('📝 Manual steps required:');
  console.log('1. Set up email credentials in .env file');
  console.log('2. Verify OTP from email');
  console.log('3. Complete login flow');
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This test requires Node.js 18+ or install node-fetch');
  console.log('Run: npm install node-fetch');
  process.exit(1);
}

testAuthFlow().catch(console.error);
