import { Router } from 'express';
import {
  getUserProfile,
  updateUserProfile,
  deleteUserProfile,
  addChild,
  getAllChildren,
  getChildDetails,
  updateChild,
  deleteChild
} from '../controllers/index';
import { verifyToken, verifyTokenForParent } from '../middlewares/auth.middleware';

const router = Router();

router.use(verifyTokenForParent);
// -------- User Profile API's (Parent/Admin) ----------------------
router.get('/profile', getUserProfile);
router.put('/profile', updateUserProfile);
router.delete('/profile', deleteUserProfile);

//--------- Child Management API's (Parent Only) ----------------------
router.post('/add-child', addChild);
router.get('/get-all-children', getAllChildren);
router.get('/:childId', getChildDetails);
router.put('/:childId', updateChild);
router.delete('/:childId', deleteChild);

export default router;

