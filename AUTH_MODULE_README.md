# Authentication Module Documentation

## Overview
This authentication module provides a complete user authentication system with signup, login, and password reset functionality using JWT tokens and email OTP verification.

## Features
- User registration with email verification
- Secure login with JWT tokens
- Password reset with OTP verification
- Password encryption using bcrypt
- Input validation and error handling
- Role-based access control
- Protected routes middleware

## User Schema
The user model includes the following fields:
- `fullname`: User's full name (required)
- `username`: Unique username (required, 3-30 characters)
- `email`: Unique email address (required, validated)
- `phoneNumber`: Phone number (required)
- `password`: Encrypted password (required, min 6 characters)
- `dob`: Date of birth (required)
- `gender`: Male/Female/Other (required)
- `role`: Parent/Child/Admin (default: Parent)
- `isVerified`: Email verification status (default: false)
- `otp`: One-time password for verification
- `otpCreatedAt`: OTP creation timestamp

## API Endpoints

### Authentication Routes
Base URL: `/api/auth`

#### 1. User Signup
**POST** `/api/auth/signup`

**Request Body:**
```json
{
  "fullname": "<PERSON>",
  "username": "johndoe",
  "email": "<EMAIL>",
  "phoneNumber": "+1234567890",
  "password": "password123",
}
```

**Response:**
```json
{
  "success": true,
  "message": "User created successfully. Please verify your email with the OTP sent to your email address.",
  "user": {
    "id": "user_id",
    "fullname": "John Doe",
    "username": "johndoe",
    "email": "<EMAIL>",
    "isVerified": false
  }
}
```

#### 2. Verify Signup OTP
**POST** `/api/auth/verify-signup-otp`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "otp": "1234"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Email verified successfully",
  "user": {
    "id": "user_id",
    "fullname": "John Doe",
    "username": "johndoe",
    "email": "<EMAIL>",
    "isVerified": true,
    "role": "Parent"
  },
  "token": "jwt_token_here"
}
```

#### 3. User Login
**POST** `/api/auth/login`

**Request Body:**
```json
{
  "username": "johndoe",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "user": {
    "id": "user_id",
    "fullname": "John Doe",
    "username": "johndoe",
    "email": "<EMAIL>",
    "phoneNumber": "+1234567890",
    "dob": "1990-01-01T00:00:00.000Z",
    "gender": "Male",
    "role": "Parent",
    "isVerified": true
  },
  "token": "jwt_token_here"
}
```

### Password Reset Routes

#### 4. Generate Password Reset OTP
**POST** `/api/auth/forgot-password/generate-otp`

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "message": "OTP sent to your email"
}
```

#### 5. Verify Password Reset OTP
**POST** `/api/auth/forgot-password/verify-otp`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "otp": "1234"
}
```

**Response:**
```json
{
  "message": "OTP verified"
}
```

#### 6. Reset Password
**POST** `/api/auth/forgot-password/reset`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "newpassword123",
  "confirmPassword": "newpassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password reset successfully"
}
```

### User Profile Routes
Base URL: `/api/user`

#### 7. Get User Profile (Protected)
**GET** `/api/user/profile`

**Headers:**
```
Authorization: Bearer jwt_token_here
```

**Response:**
```json
{
  "success": true,
  "message": "Profile retrieved successfully",
  "user": {
    "id": "user_id",
    "fullname": "John Doe",
    "username": "johndoe",
    "email": "<EMAIL>",
    "phoneNumber": "+1234567890",
    "dob": "1990-01-01T00:00:00.000Z",
    "gender": "Male",
    "role": "Parent",
    "isVerified": true
  }
}
```

#### 8. Update User Profile (Protected)
**PUT** `/api/user/profile`

**Headers:**
```
Authorization: Bearer jwt_token_here
```

**Request Body:**
```json
{
  "fullname": "John Smith",
  "phoneNumber": "+1987654321",
  "dob": "1990-01-01",
  "gender": "Male"
}
```

## Environment Variables
Create a `.env` file with the following variables:

```env
# Server Configuration
PORT=5001

# Database Configuration
MONGO_URI=mongodb://localhost:27017/piggy-portfolio

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# Email Configuration (for OTP)
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
```

## Authentication Middleware
The module includes middleware for protecting routes:

```typescript
import { authenticateToken, authorizeRole } from '../middlewares/auth.middleware';

// Protect route with authentication
router.get('/protected', authenticateToken, handler);

// Protect route with role-based access
router.get('/admin-only', authenticateToken, authorizeRole('Admin'), handler);
```

## Error Handling
All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description"
}
```

Common HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (invalid credentials/token)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found
- `409`: Conflict (duplicate username/email)
- `500`: Internal Server Error

## Security Features
- Password hashing with bcrypt (salt rounds: 12)
- JWT token authentication
- OTP expiration (2 minutes)
- Input validation and sanitization
- Rate limiting (configured in server)
- CORS protection
- Helmet security headers

## Usage Example
1. User signs up with required information
2. System sends OTP to user's email
3. User verifies email with OTP
4. User can now login and receive JWT token
5. JWT token is used for accessing protected routes
6. For password reset, user requests OTP, verifies it, then sets new password

## File Structure
```
src/
├── controllers/
│   ├── auth.controller.ts
│   └── helper/
│       └── OTP.ts
├── interfaces/
│   └── user.interface.ts
├── middlewares/
│   └── auth.middleware.ts
├── models/
│   └── User.ts
└── routes/
    ├── auth.routes.ts
    ├── user.routes.ts
    └── index.ts
```
