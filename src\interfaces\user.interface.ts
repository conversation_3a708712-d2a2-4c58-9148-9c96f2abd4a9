import { Document, Types } from 'mongoose';
import { TypesOfUsersEnum } from "../types/userTypes";

export interface IUser extends Document {
  _id: Types.ObjectId;
  fullname: string;
  username: string;
  email: string;
  phoneNumber: string;
  password: string;
  role: TypesOfUsersEnum;
  isVerified: boolean;
  otp: string;
  otpCreatedAt: Date;
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

export interface ISignupRequest {
  fullname: string;
  username: string;
  email: string;
  phoneNumber: string;
  password: string;
  role  : TypesOfUsersEnum;
}

export interface ILoginRequest {
  username: string;
  password: string;
}

export interface IForgotPasswordRequest {
  email: string;
  password: string;
  confirmPassword: string;
}

export interface IOTPRequest {
  email: string;
  otp: string;
}

export interface IAuthResponse {
  success: boolean;
  message: string;
  user?: Partial<IUser>;
  token?: string;
}


export interface IUserResponse {
  success: boolean;
  message: string;
  user?: {
    id: string;
    fullname: string;
    username: string;
    email: string;
    phoneNumber: string;
    role: string;
    isVerified: boolean;
    createdAt: Date;
    updatedAt: Date;
  };
}

export interface IUpdateUserRequest {
  fullname?: string;
  email?: string;
  phoneNumber?: string;
}

export interface IDeleteUserRequest {
  password: string;
}
