import type { Request, Response } from 'express';
import nodemailer from 'nodemailer';
import dotenv from 'dotenv';
import { StatusCodes } from 'http-status-codes';
import User from '../../models/User';
import {
  IAuthResponse
} from '../../interfaces/user.interface';
dotenv.config();

export const sendOtpEmail = async (email: string, otp: string, purpose: 'signup' | 'reset' = 'reset') => {
  const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  });

  const subject = purpose === 'signup' ? 'Email Verification OTP' : 'Password Reset OTP';
  const text = purpose === 'signup'
    ? `Welcome! Your OTP for email verification is: ${otp}. This OTP will expire in 2 minutes.`
    : `Your OTP for password reset is: ${otp}. This OTP will expire in 2 minutes.`;

  const mailOptions = {
    from: process.env.EMAIL_USER,
    to: email,
    subject,
    text
  };

  await transporter.sendMail(mailOptions);
};

// Generate OTP and store it in the user's document (when user click on reset password it will generate OTP and send it to user's email)
export const resetPasswordGenerateOTP = async (req: Request, res: Response) => {
  const { email } = req.body;

  try {
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Email not found' });
    }

    // Generate 4-digit OTP
    const otp = Math.floor(1000 + Math.random() * 9000).toString();

    // Send OTP to user's email
    await sendOtpEmail(email, otp);

    // Set OTP value to null before storing it (ensures old value is cleared)
    user['otp'] = "";  
    user['otpCreatedAt'] = new Date();   // Store the current time as OTP generation time
    user['otp'] = otp;   // Store the new OTP
    await user.save();

    return res.status(StatusCodes.OK).json({ message: 'OTP sent to your email' });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error sending OTP', error });
  }
};


// Verify OTP function
export const verifyOtp = async (req: Request, res: Response) => {
  const { email, otp } = req.body;

  try {
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'Email not found' });
    }

    if (!user['otp']) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'OTP has not been generated. Click Resend to generate a new one.' });
    }

    // Check if OTP is expired ... 2 minutes expiration time
    console.log("OTP Created At:", user['otpCreatedAt']);
    const otpCreatedAt = user['otpCreatedAt'] instanceof Date ? user['otpCreatedAt'].getTime() : 0;
    const otpAge = new Date().getTime() - otpCreatedAt;
    console.log("OTP Age in milliseconds:", otpAge); 
    const otpExpirationTime = 2 * 60 * 1000; // 2 minutes in milliseconds
    if (otpAge > otpExpirationTime) {
      user['otp'] = "";  
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'OTP has expired. Please request a new OTP' });
    }

    if (user['otp'] !== otp) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'OTP does not match' });
    }

    user['otp'] = "";
    await user.save();

    return res.status(StatusCodes.OK).json({ message: 'OTP verified' });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error verifying OTP', error });
  }
};

// Verify OTP for signup
export const verifySignupOTP = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { email, otp } = req.body;

    if (!email || !otp) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Email and OTP are required'
      } as IAuthResponse);
    }

    const user = await User.findOne({ email });
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'User not found'
      } as IAuthResponse);
    }

    if (!user.otp) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'OTP has not been generated. Please request a new OTP.'
      } as IAuthResponse);
    }

    // Check if OTP is expired (2 minutes)
    const otpAge = new Date().getTime() - user.otpCreatedAt.getTime();
    const otpExpirationTime = 2 * 60 * 1000; // 2 minutes in milliseconds
    
    if (otpAge > otpExpirationTime) {
      user.otp = '';
      await user.save();
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'OTP has expired. Please request a new OTP.'
      } as IAuthResponse);
    }

    if (user.otp !== otp) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid OTP'
      } as IAuthResponse);
    }

    // Verify user and clear OTP
    user.isVerified = true;
    user.otp = '';
    await user.save();

    // Generate JWT token
    //const token = generateToken(user._id?.toString() || '');

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Email verified successfully',
      user: {
        id: user._id,
        fullname: user.fullname,
        username: user.username,
        email: user.email,
        isVerified: user.isVerified,
        role: user.role
      },
    } as IAuthResponse);

  } catch (error) {
    console.error('Verify OTP error:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Internal server error during OTP verification'
    } as IAuthResponse);
  }
};


