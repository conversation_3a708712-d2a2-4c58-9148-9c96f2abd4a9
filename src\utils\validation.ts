// Validation utilities for authentication

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password: string): { isValid: boolean; message?: string } => {
  if (password.length < 6) {
    return { isValid: false, message: 'Password must be at least 6 characters long' };
  }
  
  if (password.length > 128) {
    return { isValid: false, message: 'Password must be less than 128 characters' };
  }
  
  // Check for at least one letter and one number
  const hasLetter = /[a-zA-Z]/.test(password);
  const hasNumber = /\d/.test(password);
  
  if (!hasLetter || !hasNumber) {
    return { isValid: false, message: 'Password must contain at least one letter and one number' };
  }
  
  return { isValid: true };
};

export const validateUsername = (username: string): { isValid: boolean; message?: string } => {
  if (username.length < 3) {
    return { isValid: false, message: 'Username must be at least 3 characters long' };
  }
  
  if (username.length > 30) {
    return { isValid: false, message: 'Username must be less than 30 characters' };
  }
  
  const usernameRegex = /^[a-zA-Z0-9_]+$/;
  if (!usernameRegex.test(username)) {
    return { isValid: false, message: 'Username can only contain letters, numbers, and underscores' };
  }
  
  return { isValid: true };
};

export const validatePhoneNumber = (phoneNumber: string): { isValid: boolean; message?: string } => {
  const phoneRegex = /^\+?[\d\s-()]+$/;
  if (!phoneRegex.test(phoneNumber)) {
    return { isValid: false, message: 'Please enter a valid phone number' };
  }
  
  // Remove all non-digit characters to check length
  const digitsOnly = phoneNumber.replace(/\D/g, '');
  if (digitsOnly.length < 10 || digitsOnly.length > 15) {
    return { isValid: false, message: 'Phone number must be between 10 and 15 digits' };
  }
  
  return { isValid: true };
};

export const validateAge = (dob: Date): { isValid: boolean; message?: string } => {
  const today = new Date();
  const birthDate = new Date(dob);
  
  if (birthDate > today) {
    return { isValid: false, message: 'Date of birth cannot be in the future' };
  }
  
  const age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    // Subtract 1 if birthday hasn't occurred this year
  }
  
  if (age < 13) {
    return { isValid: false, message: 'User must be at least 13 years old' };
  }
  
  if (age > 120) {
    return { isValid: false, message: 'Please enter a valid date of birth' };
  }
  
  return { isValid: true };
};

export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};

export const validateSignupData = (data: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // Required fields check
  const requiredFields = ['fullname', 'username', 'email', 'phoneNumber', 'password', 'role'];
  for (const field of requiredFields) {
    if (!data[field]) {
      errors.push(`${field} is required`);
    }
  }
  
  if (errors.length > 0) {
    return { isValid: false, errors };
  }
  

  // Validate email
  if (!validateEmail(data.email)) {
    errors.push('Please enter a valid email address');
  }
  
  // Validate password
  const passwordValidation = validatePassword(data.password);
  if (!passwordValidation.isValid) {
    errors.push(passwordValidation.message!);
  }
  
  // Validate username
  const usernameValidation = validateUsername(data.username);
  if (!usernameValidation.isValid) {
    errors.push(usernameValidation.message!);
  }
  
  // Validate phone number
  const phoneValidation = validatePhoneNumber(data.phoneNumber);
  if (!phoneValidation.isValid) {
    errors.push(phoneValidation.message!);
  }
  return { isValid: errors.length === 0, errors };
};

