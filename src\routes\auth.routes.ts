import { Router } from 'express';
import { 
  signup, 
  loginAsParent, 
  forgotPassword 
} from '../controllers/index';
import { 
  resetPasswordGenerateOTP, 
  verifyOtp,
  verifySignupOTP, 
} from '../controllers/helper/OTP';

const router = Router();

// Authentication Routes
router.post('/signup', signup);
router.post('/verify-signup-otp', verifySignupOTP);
router.post('/resend-otp', resetPasswordGenerateOTP);  // for both signup and forgot password
router.post('/login', loginAsParent);

// Password Reset Routes
router.post('/forgot-password', resetPasswordGenerateOTP);
router.post('/forgot-password/verify-otp', verifyOtp);
router.post('/forgot-password/reset', forgotPassword);

export default router;
